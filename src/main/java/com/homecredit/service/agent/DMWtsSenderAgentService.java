package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.Attribute;
import com.homecredit.model.MessageWrapper;
import com.homecredit.model.DMWtsMessage;
import com.homecredit.model.BatchData;
import com.homecredit.service.AbstractSenderAgentService;
import com.homecredit.service.ExtApiGwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.DM_WTS.enabled", havingValue = "true")
public class DMWtsSenderAgentService extends AbstractSenderAgentService<DMWtsMessage> {

    private final RabbitTemplate rabbitTemplate;
    private final ExtApiGwService extApiGwService;

    public DMWtsSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate,
            ExtApiGwService extApiGwService) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
        this.extApiGwService = extApiGwService;
    }

    @Override
    public Agent getAgent() {
        return Agent.DM_WTS;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for DM_WTS agent. Total processed: {} records in {} batches",
                         totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for DM_WTS agent (total so far: {})",
                     batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            for (Map<String, Object> row : newRows) {

                Long id = (Long) getColumnValue(row, "ID");
                log.debug("Processing message with ID {} ...", id);
                DMWtsMessage message = prepareMessage(row);

                if (isMessageExpired(message)) {
                    addToBatch(MessageStatus.FAILED, message, "Message is expired", FailedStatus.EXPIRED);
                } else if (message.getContactId() == null) {
                    log.debug("Message with ID {} has null contactId", id);
                    if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                        log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                        addToBatch(MessageStatus.FAILED, message, "Message has null externalID for more than 30 minutes", FailedStatus.EXPIRED);
                    } else {
                        log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                    }
                } else {
                    sendMessageToRabbit(message);
                }

                if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                    processBatch();
                }
            }

            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    private void sendMessageToRabbit(DMWtsMessage message) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessage(Collections.singletonList(message));

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        addToBatch(PROCESSED, message);
    }


    private DMWtsMessage prepareMessage(Map<String, Object> row) {
        String cuid = (String) getColumnValue(row, "cuid");

        DMWtsMessage message = new DMWtsMessage();
        message.setId((Long) getColumnValue(row, "ID"));
        message.setExternalId("WTS_" + getColumnValue(row, "contact_id"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setLeadId((String) getColumnValue(row, "lead_id"));
        message.setIdentityId((String) getColumnValue(row, "identity_id"));
        message.setVisitorId((String) getColumnValue(row, "visitor_id"));
        message.setRecipient((String) getColumnValue(row, "PHONE_NUMBER"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        message.setMessageCode((String) getColumnValue(row, "MESSAGECODE"));
        message.setCuid(cuid);
        message.setText((String) getColumnValue(row, "MSG_TEXT"));
        message.setExpires(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "ExpiresOnDttm"))));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setPriority((String) getColumnValue(row, "PRIORITY"));
        message.setReportLevel(applicationConfig.getPayload().get(getAgent()).getReportLevel());
        List<Attribute> attributes = new ArrayList<>();
        if (cuid != null) {
            attributes.add(new Attribute("CUID", cuid));
        }
        attributes.add(new Attribute("CHANNEL", (String) getColumnValue(row, "CHANNEL")));
        attributes.add(new Attribute("FEATURE", (String) getColumnValue(row, "FEATURE")));
        attributes.add(new Attribute("JATIS_ACCOUNT_ID", (String) getColumnValue(row, "JATIS_ACCOUNT_ID")));
        attributes.add(new Attribute("WA_TEMPLATE_ID", (String) getColumnValue(row, "JATIS_TPL_ID")));
        attributes.add(new Attribute("WA_LANGUAGE", (String) getColumnValue(row, "TPL_LANGUAGE")));
        if (getColumnValue(row, "jts_media_content_type") != null) {
            attributes.add(new Attribute("WA_MEDIA_CONTENT_TYPE", (String) getColumnValue(row, "jts_media_content_type")));
        }
        if (getColumnValue(row, "jts_media_link") != null) {
            attributes.add(new Attribute("WA_MEDIA_LINK", (String) getColumnValue(row, "jts_media_link")));
        }
        if (getColumnValue(row, "JTS_MEDIA_NAME") != null) {
            attributes.add(new Attribute("WA_MEDIA_NAME", (String) getColumnValue(row, "JTS_MEDIA_NAME")));
        }
        if (getColumnValue(row, "JTS_HEADER_PARAM1") != null) {
            attributes.add(new Attribute("WA_HEADER_PARAM_VALUE_1", (String) getColumnValue(row, "JTS_HEADER_PARAM1")));
        }
        for (int i = 1; i <= 10; i++) {
            if (getColumnValue(row, "JTS_BODY_PARAM" + i) != null) {
                attributes.add(new Attribute("WA_BODY_PARAM_VALUE_" + i, (String) getColumnValue(row, "JTS_BODY_PARAM" + i)));
            }
            if (getColumnValue(row, "JTS_BUTTON_" + i + "_PARAM_VALUE") != null) {
                attributes.add(new Attribute("WA_BUTTON_PARAM_VALUE_" + i + "_1", (String) getColumnValue(row, "JTS_BUTTON_" + i + "_PARAM_VALUE")));
            }
        }
        message.setAttributes(attributes);
        return message;
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<DMWtsMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<DMWtsMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getContactId() != null ? data.message().getExternalId() : "null");
                stmt.setString(2, data.errorMessage().substring(0, Math.min(data.errorMessage().length(), 36)));
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<DMWtsMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getContactId() != null ? data.message().getExternalId() : null);
                stmt.setString(3, data.message().getIdentityId());
                stmt.setString(4, data.message().getVisitorId());
                stmt.setString(5, data.message().getLeadId());
                stmt.setString(6, data.errorMessage());
                stmt.setLong(7, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }

    private boolean isMessageExpired(DMWtsMessage message) {
        if (message.getExpires() == null) {
            return false;
        }
        log.debug("Checking if message is expired. expires = [{}]", message.getExpires());
        return message.getExpires().isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }
}
