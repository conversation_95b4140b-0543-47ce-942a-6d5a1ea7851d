package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.BatchData;
import com.homecredit.model.DMGenesysMessage;
import com.homecredit.model.calllisttype.CallListType;
import com.homecredit.model.calllisttype.CallListTypeCache;
import com.homecredit.model.calllisttype.CallListTypeCategory;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.DM_GEN.enabled", havingValue = "true")
public class DMGenesysSenderAgentService extends AbstractSenderAgentService<DMGenesysMessage> {

    private final KafkaTemplate<String, String> kafkaTemplate;

    public DMGenesysSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            KafkaTemplate<String, String> kafkaTemplate) {
        super(applicationConfig, dataSource, objectMapper);
        this.kafkaTemplate = kafkaTemplate;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    @Override
    public Agent getAgent() {
        return Agent.DM_GEN;
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for DM_GEN agent. Total processed: {} records in {} batches",
                         totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for DM_GEN agent (total so far: {})",
                     batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            for (Map<String, Object> row : newRows) {

                DMGenesysMessage message = prepareMessage(row);
                log.debug("Processing message with ID {} ...", message.getId());

                if (message.getContactId() == null) {
                    if (isTooLateForProcessingContactId((ZonedDateTime) getColumnValue(row, "GenerateDateDttm"))) {
                        String errorMsg = "Message with ID " + message.getId() + " has null contact_id and is more than " + applicationConfig.getContactIdTimeout() + "  minutes old, marking as expired";
                        addToBatch(MessageStatus.FAILED, message, errorMsg, FailedStatus.EXPIRED);
                    } else {
                        log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                    }
                    
                    if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                        processBatch();
                    }
                    continue;
                }

                Set<CallListType> callListTypes = getCallListTypes(message);
                if (callListTypes == null) {
                    if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                        processBatch();
                    }
                    continue;
                }
                Map<String, String> businessAttributes = getBusinessAttributes(row);
                if (!validateWithCache(row, callListTypes, businessAttributes, message)) {
                    if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                        processBatch();
                    }
                    continue;
                }
                DMGenesysMessage enrichedMessage = enrichMessage(message, row, callListTypes, businessAttributes);

                sendMessageToKafka(enrichedMessage);

                if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                    processBatch();
                }
            }

            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    private Set<CallListType> getCallListTypes(DMGenesysMessage message) {
        try {
            Set<CallListType> callListTypes = CallListTypeCache.get(message.getCallListType());
            if (!message.getCallListType().equalsIgnoreCase("GENERAL")) {
                callListTypes.addAll(CallListTypeCache.get("GENERAL"));
            }
            return callListTypes;
        } catch (Exception e) {
            String errorMsg = "ERR_01 - Not supported CALL_LIST_TYPE " + message.getCallListType() + " or not found in cache";
            addToBatch(MessageStatus.FAILED, message, errorMsg, FailedStatus.INVALID);
            return null;
        }
    }

    private Set<CallListType> getCallListTypes(String codeCallListType) {
        try {
            Set<CallListType> callListTypes = CallListTypeCache.get(codeCallListType);
            if (!codeCallListType.equalsIgnoreCase("GENERAL")) {
                callListTypes.addAll(CallListTypeCache.get("GENERAL"));
            }
            return callListTypes;
        } catch (Exception e) {
            return new HashSet<>();
        }
    }

    private void sendMessageToKafka(DMGenesysMessage message) {
        String key = message.getOtherFields().get("il_communication_id").toString();
        if (key == null || key.isEmpty()) {
            addToBatch(MessageStatus.FAILED, message, "Null key - cannot send message to kafka", FailedStatus.INVALID);
            return;
        }
        log.debug("Preparing to send message with ID {} to Kafka", key);

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(message);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);
        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(kafkaTemplate.getDefaultTopic(), key, jsonMessage);
            kafkaTemplate.send(record);
            log.info("Message sent to kafka");
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "ERR_03 - Fail to sent to Kafka error " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        addToBatch(PROCESSED, message);
    }


    private DMGenesysMessage prepareMessage(Map<String, Object> row) {
        DMGenesysMessage message = new DMGenesysMessage();

        message.setId((Long) getColumnValue(row, "ID"));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setLeadId((String) getColumnValue(row, "lead_id"));
        message.setIdentityId((String) getColumnValue(row, "identity_id"));
        message.setVisitorId((String) getColumnValue(row, "visitor_id"));
        message.setCallListType((String) getColumnValue(row, "call_list_type"));

        return message;
    }

    private DMGenesysMessage enrichMessage(DMGenesysMessage message, Map<String, Object> row, Set<CallListType> callListTypes, Map<String, String> businessAttributes) {
        Map<String, Object> messageFields = new HashMap<>();
        Map<String, Object> recordFields = new HashMap<>();

        //dynamic mapping from cache
        Map<String, CallListType> callListTypeMap = callListTypes.stream().collect(Collectors.toMap(CallListType::getAttributeName, Function.identity()));
        callListTypeMap.forEach((attributeName, attribute) -> {
            if (attribute.getCategory() == CallListTypeCategory.BUSINESS) {
                String value = businessAttributes.get(attributeName.toUpperCase());
                if (attribute.getAttributeType().equals("NUMBER")) {
                    if (attribute.getPayloadSection() != null && attribute.getPayloadSection().equals("records")) {
                        recordFields.put(attributeName, getLong(value));
                    } else {
                        messageFields.put(attributeName, getLong(value));
                    }
                } else { // STRING, DATE
                    if (attribute.getPayloadSection() != null && attribute.getPayloadSection().equals("records")) {
                        recordFields.put(attributeName, value);
                    } else {
                        messageFields.put(attributeName, value);
                    }
                }
            } else {
                if (attribute.getAttributeType().equals("NUMBER")) {
                    if (attribute.getPayloadSection() != null && attribute.getPayloadSection().equals("records")) {
                        recordFields.put(attributeName, getColumnValue(row, attributeName));
                    } else {
                        messageFields.put(attributeName, getColumnValue(row, attributeName));
                    }
                } else if (attribute.getAttributeType().equals("DATE")) {
                    if (attribute.getPayloadSection() != null && attribute.getPayloadSection().equals("records")) {
                        recordFields.put(attributeName, parseToString((ZonedDateTime) getColumnValue(row, attributeName)));
                    } else {
                        messageFields.put(attributeName, parseToString((ZonedDateTime) getColumnValue(row, attributeName)));
                    }
                } else { //STRING
                    if (attribute.getPayloadSection() != null && attribute.getPayloadSection().equals("records")) {
                        recordFields.put(attributeName, getColumnValue(row, attributeName));
                    } else {
                        messageFields.put(attributeName, getColumnValue(row, attributeName));
                    }
                }
            }
        });

        //custom mapping - override values
        recordFields.put("resptracking_id", message.getId());
        if ((Long) getColumnValue(row, "is_callback_request") == 0L) {
            recordFields.put("record_type", 2L);
        } else {
            recordFields.put("record_type", 6L);
            recordFields.put("switch_id", applicationConfig.getPayload().get(getAgent()).getSwitchId());
        }

        messageFields.put("contact_id", message.getContactId());
        messageFields.put("identity_id", getColumnValue(row, "identity_id"));
        if (getColumnValue(row, "cuid") == null) {
            messageFields.put("cuid", "");
        } else {
            messageFields.put("cuid", getColumnValue(row, "cuid"));
        }
        if (callListTypes.stream()
                .filter(it -> it.getAttributeName().equals("call_type"))
                .map(CallListType::getAttributeType)
                .findFirst()
                .orElse("")
                .equals("STRING")) {
            messageFields.put("call_type", "UNKNOWN");
        } else {
            messageFields.put("call_type", 0L);
        }
        messageFields.put("communication_start", parseToString((ZonedDateTime) getColumnValue(row, "communication_start")));
        messageFields.put("communication_end", parseToString((ZonedDateTime) getColumnValue(row, "communication_end")));
        messageFields.put("campaign_start", parseToString((ZonedDateTime) getColumnValue(row, "campaign_start")));
        messageFields.put("campaign_end", parseToString((ZonedDateTime) getColumnValue(row, "campaign_end")));
        messageFields.put("il_communication_id", getColumnValue(row, "ID"));
        messageFields.put("date_effective", parseToString(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone()))));
        messageFields.put("call_source", getColumnValue(row, "call_source"));
        messageFields.put("code_call_list_type", getColumnValue(row, "code_call_list_type"));

        DMGenesysMessage.Record record = new DMGenesysMessage.Record();
        record.setOtherFields(recordFields);
        message.setRecords(Arrays.asList(record));
        message.setOtherFields(messageFields);

        return message;
    }

    private boolean validateWithCache(Map<String, Object> row, Set<CallListType> callListTypes, Map<String, String> businessAttributes, DMGenesysMessage message) {
        List<String> failedAttributes = new ArrayList<>();
        callListTypes.forEach(type -> {
            if (type.getCategory() == CallListTypeCategory.BUSINESS) {
                if (businessAttributes == null) {
                    failedAttributes.add(type.getAttributeName());
                } else {
                    String businessValue = businessAttributes.get(type.getAttributeName().toUpperCase());
                    if (!validateBusiness(row, type, businessValue)) {
                        failedAttributes.add(type.getAttributeName());
                    }
                }
            } else {
                if (!validateTaskOrTechnical(row, type)) {
                    failedAttributes.add(type.getAttributeName());
                }
            }
        });
        if (!failedAttributes.isEmpty()) {
            String failedAttrString = String.join(", ", failedAttributes);
            addToBatch(MessageStatus.FAILED, message, "ERR_02_1 - Not possible to validate - attributes " +
                            failedAttrString + " Mandatory / wrong data type",
                    FailedStatus.INVALID);
            return false;
        }
        log.debug("Validation passed successfully");
        return true;
    }

    private boolean validateBusiness(Map<String, Object> row, CallListType type, String value) {
        if (value == null) {
            return !type.getMandatoryFlag();
        }
        // disable type check for business attributes
//        else if (type.getAttributeType().equals("DATE")) {
//            return getDateTime(value) != null;
//        } else if (type.getAttributeType().equals("NUMBER")) {
//            return getLong(value) != null;
//        }
        return true;
    }

    private boolean validateTaskOrTechnical(Map<String, Object> row, CallListType type) {
        Object object = getColumnValue(row, type.getAttributeName());
        if (object == null) {
            return !type.getMandatoryFlag();
        } else if (type.getAttributeType().equals("STRING")) {
            return object instanceof String;
        } else if (type.getAttributeType().equals("DATE")) {
            return object instanceof ZonedDateTime;
        } else if (type.getAttributeType().equals("NUMBER")) {
            return object instanceof Long;
        }
        return true;
    }


    /**
     * Receive all unprocessed messages from database
     *
     * @return List of {@link DMGenesysMessage}
     */
    @Override
    protected List<Map<String, Object>> getNewRows(Agent agent) {
        log.debug("{} - Retrieving new rows from database ...", agent);
        List<Map<String, Object>> rows = new ArrayList<>();
        try (Connection conn = dataSource.getConnection()) {
            try (Statement statement = conn.createStatement();
                 ResultSet resultSet = statement.executeQuery(applicationConfig.getQuery().get(getAgent()).getGetNonProcessedRecords())) {
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (resultSet.next()) {
                    Map<String, Object> rowMap = new HashMap<>();

                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value;

                        int columnType = metaData.getColumnType(i);
                        switch (columnType) {
                            case Types.INTEGER:
                            case Types.NUMERIC:
                                value = resultSet.getLong(i);
                                break;
                            case Types.FLOAT:
                                value = resultSet.getFloat(i);
                                break;
                            case Types.DOUBLE:
                            case Types.DECIMAL:
                                value = resultSet.getDouble(i);
                                break;
                            case Types.DATE:
                            case Types.TIMESTAMP:
                                value = getDateTime(resultSet.getString(i));
                                break;
                            default:
                                value = resultSet.getString(i);
                                break;
                        }
                        rowMap.put(columnName, value);
                    }

                    // custom mapping start - validation is done before message is created
                    Set<CallListType> callListTypes = getCallListTypes((String) getColumnValue(rowMap, "code_call_list_type"));
                    if (callListTypes.stream()
                            .filter(it -> it.getAttributeName().equals("call_type"))
                            .map(CallListType::getAttributeType)
                            .findFirst()
                            .orElse("")
                            .equals("STRING")) {
                        rowMap.put("CALL_TYPE", "UNKNOWN");
                    } else {
                        rowMap.put("CALL_TYPE", 0L);
                    }
                    if (rowMap.get("IS_CALLBACK_REQUEST") != null && (Long) rowMap.get("IS_CALLBACK_REQUEST") == 0L) {
                        rowMap.put("RECORD_TYPE", 2L);
                    } else {
                        rowMap.put("RECORD_TYPE", 6L);
                        rowMap.put("SWITCH_ID", applicationConfig.getPayload().get(getAgent()).getSwitchId());
                    }
                    rowMap.put("DATE_EFFECTIVE", ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
                    rowMap.put("IL_COMMUNICATION_ID", rowMap.get("ID"));
                    rowMap.put("RESPTRACKING_ID", rowMap.get("ID"));

                    // custom mapping end
                    rows.add(rowMap);
                }
            }
        } catch (SQLException e) {
            log.error("{} - Exception during executing query", agent, e);
        }

        log.debug("{} - Found {} new rows for processing", agent, rows.size());
        return rows;
    }

    private Map<String, String> getBusinessAttributes(Map<String, Object> row) {
        Map<String, String> businessAttributes = new HashMap<>();
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement statement = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getGetBusinessAttributes())) {
                statement.setString(1, (String) getColumnValue(row, "subject_id"));
                statement.setString(2, (String) getColumnValue(row, "rtc_id"));
                ResultSet resultSet = statement.executeQuery();
                while (resultSet.next()) {
                    String name = resultSet.getString("Attribute_name");
                    String value = resultSet.getString("Attribute_Value");
                    businessAttributes.put(name.toUpperCase(), value);
                }

            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
            return null;
        }
        return businessAttributes;
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<DMGenesysMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<DMGenesysMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getContactId() != null ? data.message().getContactId() : "null");
                stmt.setString(2, data.errorMessage().substring(0, Math.min(data.errorMessage().length(), 36)));
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<DMGenesysMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getContactId());
                stmt.setString(3, data.message().getIdentityId());
                stmt.setString(4, data.message().getVisitorId());
                stmt.setString(5, data.message().getLeadId());
                stmt.setString(6, data.errorMessage());
                stmt.setLong(7, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }
}
