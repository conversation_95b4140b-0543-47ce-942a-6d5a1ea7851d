package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.DMMessageWrapper;
import com.homecredit.model.DMPushAdditionalData;
import com.homecredit.model.DMPushMessage;
import com.homecredit.model.DMPushMessageAttribute;
import com.homecredit.model.BatchData;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.SimpleResourceHolder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.nio.ByteBuffer;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.DM_PSH.enabled", havingValue = "true")
public class DMPushSenderAgentService extends AbstractSenderAgentService<DMPushMessage> {

    private final RabbitTemplate rabbitTemplate;

    public DMPushSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    @Override
    public Agent getAgent() {
        return Agent.DM_PSH;
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for DM_PSH agent. Total processed: {} records in {} batches",
                         totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for DM_PSH agent (total so far: {})",
                     batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            for (Map<String, Object> row : newRows) {

                Long id = (Long) getColumnValue(row, "ID");
                log.debug("Processing message with ID {} ...", id);
                DMPushMessage message = prepareMessage(row);

                if (message.getContactId() == null) {
                    log.debug("Message with ID {} has null contactId", id);
                    if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                        log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                        String errorMsg = "Message has null contactId for more than" + applicationConfig.getContactIdTimeout() + "minutes";
                        addToBatch(MessageStatus.FAILED, message, errorMsg, FailedStatus.EXPIRED);
                    } else {
                        log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                    }
                } else if (message.getPhoneNumber() == null) {
                    addToBatch(MessageStatus.FAILED, message, "Message is invalid - Phone number is null", FailedStatus.INVALID);
                } else if (isMessageExpired(message)) {
                    addToBatch(MessageStatus.FAILED, message, "Message has expired", FailedStatus.EXPIRED);
                } else {
                    sendMessageToRabbit(message);
                }

                if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                    processBatch();
                }
            }

            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    private void sendMessageToRabbit(DMPushMessage message) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        DMMessageWrapper messageWrapper = new DMMessageWrapper();
        messageWrapper.setMessages(Collections.singletonList(message));
        messageWrapper.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        messageWrapper.setPartnerId(applicationConfig.getPayload().get(getAgent()).getPartnerID());

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        SimpleResourceHolder.bind(rabbitTemplate.getConnectionFactory(), "DM_PSH");
        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", messageWrapper.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getMessageId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getMessageId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage(), FailedStatus.INVALID);
            return;
        } finally {
            SimpleResourceHolder.unbind(rabbitTemplate.getConnectionFactory());
        }
        addToBatch(PROCESSED, message);
    }


    private DMPushMessage prepareMessage(Map<String, Object> row) {
        DMPushMessage message = new DMPushMessage();
        String messageId = "SASCIPSH_" + convertToBase64((String) getColumnValue(row, "contact_id")).substring(0, 11);

        message.setPhoneNumber((String) getColumnValue(row, "PhoneNumber"));
        message.setWorkflow(applicationConfig.getPayload().get(getAgent()).getWorkflow());
        message.setMessageId(messageId);
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setLeadId((String) getColumnValue(row, "lead_id"));
        message.setIdentityId((String) getColumnValue(row, "identity_id"));
        message.setVisitorId((String) getColumnValue(row, "visitor_id"));
        message.setValidFrom(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setValidTo(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setGeneratedDateTime(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "generatedatedttm"))));

        DMPushMessageAttribute attribute = new DMPushMessageAttribute();
        attribute.setContractNum((String) getColumnValue(row, "contract_num"));
        attribute.setCuid((String) getColumnValue(row, "cuid"));
        attribute.setTitle((String) getColumnValue(row, "Title"));
        attribute.setFullDetailMessage((String) getColumnValue(row, "FullDetailMessage"));
        attribute.setShortMessage((String) getColumnValue(row, "ShortMessage"));
        attribute.setImageUrl((String) getColumnValue(row, "ImageURL"));
        attribute.setLongUrl((String) getColumnValue(row, "DeepLink"));

        message.setMessageAttribute(attribute);
        try {
            message.setMessageAttributeString(objectMapper.writeValueAsString(attribute));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        DMPushAdditionalData additionalData = new DMPushAdditionalData();
        //additionalData.setCategoryId((String) getColumnValue(row, "CategoryID")); //TODO where to get this, there is no column named CategoryID
        additionalData.setCategory((String) getColumnValue(row, "Category"));
        additionalData.setSubCategory((String) getColumnValue(row, "Subcategory"));
        additionalData.setRemark((String) getColumnValue(row, "Remark"));

        message.setAdditionalData(additionalData);
        try {
            message.setAdditionalDataString(objectMapper.writeValueAsString(additionalData));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        message.setId((Long) getColumnValue(row, "ID"));
        return message;
    }

    private String convertToBase64(String uuidString) {
        UUID uuid = UUID.fromString(uuidString);

        byte[] uuidBytes = ByteBuffer.wrap(new byte[16])
                .putLong(uuid.getMostSignificantBits())
                .putLong(uuid.getLeastSignificantBits())
                .array();

        return Base64.getEncoder().encodeToString(uuidBytes);
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<DMPushMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<DMPushMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getContactId() != null ? data.message().getContactId() : "null");
                stmt.setString(2, data.failedStatus().toString());
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<DMPushMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getContactId());
                stmt.setString(3, data.message().getIdentityId());
                stmt.setString(4, data.message().getVisitorId());
                stmt.setString(5, data.message().getLeadId());
                stmt.setString(6, data.message().getMessageId());
                stmt.setString(7, data.errorMessage());
                stmt.setLong(8, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }

    private boolean isMessageExpired(DMPushMessage message) {
        if (message.getValidFrom() == null || message.getValidTo() == null)
            return false;
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone()));
        log.debug("Checking if message is expired. now = [{}], validFrom = [{}], validTo = [{}]",
                now, message.getValidFrom(), message.getValidTo());
        return (message.getValidFrom().isAfter(now) || message.getValidTo().isBefore(now));
    }
}
