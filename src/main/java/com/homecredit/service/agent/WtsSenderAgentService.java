package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.Attribute;
import com.homecredit.model.MessageWrapper;
import com.homecredit.model.WtsMessage;
import com.homecredit.model.BatchData;
import com.homecredit.service.AbstractSenderAgentService;
import com.homecredit.service.ExtApiGwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.WTS.enabled", havingValue = "true")
public class WtsSenderAgentService extends AbstractSenderAgentService<WtsMessage> {

    private final RabbitTemplate rabbitTemplate;
    private final ExtApiGwService extApiGwService;

    public WtsSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate,
            ExtApiGwService extApiGwService) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
        this.extApiGwService = extApiGwService;
    }

    @Override
    public Agent getAgent() {
        return Agent.WTS;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for WTS agent. Total processed: {} records in {} batches",
                        totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for WTS agent (total so far: {})",
                    batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            for (Map<String, Object> row : newRows) {

                Long id = (Long) getColumnValue(row, "ID");
                log.debug("Processing message with ID {} ...", id);
                WtsMessage message = prepareMessage(row);

                if (message.getTplId() == null) {
                    log.debug("Message with ID {} has null TPL ID. Obtaining TPL ID from REST API call.", id);
                    Long tplId = extApiGwService.getTplId(id, message.getCreativeId(), getAgent());
                    message.setTplId(tplId);
                } else if (isMessageExpired(message)) {
                    addToBatch(MessageStatus.FAILED, message, "Message is expired", FailedStatus.EXPIRED);
                } else if (isMessageInvalid(message)) {
                    addToBatch(MessageStatus.FAILED, message, "Message is invalid", FailedStatus.INVALID);
                } else if (message.getContactId() == null) {
                    log.debug("Message with ID {} has null contactId", id);
                    if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                        log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                        addToBatch(MessageStatus.FAILED, message, "Message has null externalID for more than 30 minutes", FailedStatus.EXPIRED);
                    } else {
                        log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                    }
                } else {
                    message = populateMessageTemplate(message, row);
                    if (message == null) {
                        if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                            processBatch();
                        }
                        continue;
                    }
                    message = populateButtonParameters(message, row);
                    if (message == null) {
                        if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                            processBatch();
                        }
                        continue;
                    }
                    sendMessageToRabbit(message, id);
                }

                if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                    processBatch();
                }
            }

            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    private void sendMessageToRabbit(WtsMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessage(Collections.singletonList(message));

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        addToBatch(PROCESSED, message);
    }


    private WtsMessage prepareMessage(Map<String, Object> row) {
        Long tplId = null;
        if (getColumnValue(row, "TPL_ID") != null) {
            tplId = (Long) getColumnValue(row, "TPL_ID");
        }
        String cuid = (String) getColumnValue(row, "cuid");

        WtsMessage message = new WtsMessage();
        message.setExternalId("WTS_" + getColumnValue(row, "contact_id"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setRecipient((String) getColumnValue(row, "PHONE_NUMBER"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        if (getColumnValue(row, "MESSAGECODE") != null) {
            message.setMessageCode((String) getColumnValue(row, "MESSAGECODE"));
        } else {
            message.setMessageCode((String) getColumnValue(row, "MESSAGE_CODE"));
        }
        message.setCuid(cuid);
        if (getColumnValue(row, "HEADER_TEXT") != null) {
            message.setHeaderText((String) getColumnValue(row, "HEADER_TEXT"));
        }
        message.setBodyText((String) getColumnValue(row, "BODY_TEXT"));
        if (getColumnValue(row, "FOOTER_TEXT") != null) {
            message.setFooterText((String) getColumnValue(row, "FOOTER_TEXT"));
        }
        message.setExpires(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "ExpiresOnDttm"))));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setPriority((String) getColumnValue(row, "PRIORITY_LIST"));
        message.setReportLevel(applicationConfig.getPayload().get(getAgent()).getReportLevel());
        List<Attribute> attributes = new ArrayList<>();
        if (cuid != null) {
            attributes.add(new Attribute("CUID", cuid));
        }
        message.setChannel((String) getColumnValue(row, "CHANNEL"));
        attributes.add(new Attribute("CHANNEL", message.getChannel()));
        message.setFeature((String) getColumnValue(row, "FEATURE"));
        attributes.add(new Attribute("FEATURE", message.getFeature()));
        message.setJatisAccountId((String) getColumnValue(row, "JATIS_ACCOUNT_ID"));
        attributes.add(new Attribute("JATIS_ACCOUNT_ID", message.getJatisAccountId()));
        message.setJatisTemplateId((String) getColumnValue(row, "JATIS_TEMPLATE_ID"));
        attributes.add(new Attribute("WA_TEMPLATE_ID", message.getJatisTemplateId()));
        message.setTplLanguage((String) getColumnValue(row, "TPL_LANGUAGE"));
        attributes.add(new Attribute("WA_LANGUAGE", message.getTplLanguage()));
        if (getColumnValue(row, "jts_media_content_type") != null) {
            message.setJtsMediaContentType((String) getColumnValue(row, "jts_media_content_type"));
            attributes.add(new Attribute("WA_MEDIA_CONTENT_TYPE", message.getJtsMediaContentType()));
        }
        if (getColumnValue(row, "jts_media_link") != null) {
            message.setJtsMediaLink((String) getColumnValue(row, "jts_media_link"));
            attributes.add(new Attribute("WA_MEDIA_LINK", message.getJtsMediaLink()));
        }
        if (getColumnValue(row, "JTS_MEDIA_NAME") != null) {
            attributes.add(new Attribute("WA_MEDIA_NAME", (String) getColumnValue(row, "JTS_MEDIA_NAME")));
        }

        if (getColumnValue(row, "BUTTON_TYPE_1") != null) {
            message.setButtonType1((String) getColumnValue(row, "BUTTON_TYPE_1"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_2") != null) {
            message.setButtonType2((String) getColumnValue(row, "BUTTON_TYPE_2"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_3") != null) {
            message.setButtonType3((String) getColumnValue(row, "BUTTON_TYPE_3"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_4") != null) {
            message.setButtonType4((String) getColumnValue(row, "BUTTON_TYPE_4"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_5") != null) {
            message.setButtonType5((String) getColumnValue(row, "BUTTON_TYPE_5"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_6") != null) {
            message.setButtonType6((String) getColumnValue(row, "BUTTON_TYPE_6"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_7") != null) {
            message.setButtonType7((String) getColumnValue(row, "BUTTON_TYPE_7"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_8") != null) {
            message.setButtonType8((String) getColumnValue(row, "BUTTON_TYPE_8"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_9") != null) {
            message.setButtonType9((String) getColumnValue(row, "BUTTON_TYPE_9"));
        }
        if (getColumnValue(row, "BUTTON_TYPE_10") != null) {
            message.setButtonType10((String) getColumnValue(row, "BUTTON_TYPE_10"));
        }
        message.setAttributes(attributes);
        message.setTplId(tplId);
        message.setSubject((String) getColumnValue(row, "Subject_id"));
        message.setId((Long) getColumnValue(row, "ID"));
        message.setCreativeId((String) getColumnValue(row, "creative_id"));
        return message;
    }

    private WtsMessage populateMessageTemplate(WtsMessage message, Map<String, Object> row) {
        String headerText = message.getHeaderText() == null ? "" : message.getHeaderText();

        if (headerText.contains("{{1}}")) {
            String headerParam = (String) getColumnValue(row, "JTS_HEADER_PARAM1");
            if (headerParam == null || headerParam.isEmpty()) {
                log.error("Personalization value missing for header parameter JTS_HEADER_PARAM1 for message with ID {}", message.getId());
                addToBatch(MessageStatus.FAILED, message, "Personalization value missing for header parameter JTS_HEADER_PARAM1", FailedStatus.INVALID);
                return null;
            }
            headerText = headerText.replace("{{1}}", headerParam);
            message.getAttributes().add(new Attribute("WA_HEADER_PARAM_VALUE_1", headerParam));
        }

        String bodyText = message.getBodyText();
        for (int i = 1; i <= 10; i++) {
            if (bodyText.contains("{{" + i + "}}")) {
                String bodyParam = (String) getColumnValue(row, "JTS_BODY_PARAM" + i);
                if (bodyParam == null || bodyParam.isEmpty()) {
                    log.error("Personalization value missing for body parameter JTS_BODY_PARAM" + i + " for message with ID {}", message.getId());
                    addToBatch(MessageStatus.FAILED, message, "Personalization value missing for body parameter JTS_BODY_PARAM" + i, FailedStatus.INVALID);
                    return null;
                }
                bodyText = bodyText.replace("{{" + i + "}}", bodyParam);
                message.getAttributes().add(new Attribute("WA_BODY_PARAM_VALUE_" + i, bodyParam));
            }
        }

        String footerText = message.getFooterText() == null ? "" : message.getFooterText();
        message.setText(headerText + "|" + bodyText + "|" + footerText);
        return message;
    }

    private WtsMessage populateButtonParameters(WtsMessage message, Map<String, Object> row) {
        for (int i = 1; i <= 10; i++) {
            String buttonLink = (String) getColumnValue(row, "BUTTON_LINK_" + i);
            if (buttonLink != null && buttonLink.contains("{{") && buttonLink.contains("}}")) {
                String buttonParam = (String) getColumnValue(row, "JTS_BUTTON_" + i + "_PARAM_VALUE");
                if (buttonParam == null || buttonParam.isEmpty()) {
                    log.error("Personalization value missing for button parameter JTS_BUTTON_" + i + "_PARAM_VALUE for message with ID {}", message.getId());
                    addToBatch(MessageStatus.FAILED, message, "Personalization value missing for button parameter JTS_BUTTON_" + i + "_PARAM_VALUE", FailedStatus.INVALID);
                    return null;
                }
                message.getAttributes().add(new Attribute("WA_BUTTON_PARAM_VALUE_" + i + "_1", buttonParam));
            }
        }
        return message;
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<WtsMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<WtsMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getContactId() != null ? data.message().getExternalId() : "null");
                stmt.setString(2, data.errorMessage().substring(0, Math.min(data.errorMessage().length(), 36)));
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<WtsMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getContactId() != null ? data.message().getExternalId() : null);
                if (data.message().getTplId() == null) {
                    stmt.setNull(3, Types.BIGINT);
                } else {
                    stmt.setLong(3, data.message().getTplId());
                }
                setStringParameter(stmt, 4, data.message().getTplLanguage());
                setStringParameter(stmt, 5, data.message().getJatisTemplateId());
                setStringParameter(stmt, 6, data.message().getJatisAccountId());
                stmt.setString(7, data.message().getMessageCode());
                setStringParameter(stmt, 8, data.message().getChannel());
                setStringParameter(stmt, 9, data.message().getPriority());
                setStringParameter(stmt, 10, data.message().getButtonType1());
                setStringParameter(stmt, 11, data.message().getButtonType2());
                setStringParameter(stmt, 12, data.message().getButtonType3());
                setStringParameter(stmt, 13, data.message().getButtonType4());
                setStringParameter(stmt, 14, data.message().getButtonType5());
                setStringParameter(stmt, 15, data.message().getButtonType6());
                setStringParameter(stmt, 16, data.message().getButtonType7());
                setStringParameter(stmt, 17, data.message().getButtonType8());
                setStringParameter(stmt, 18, data.message().getButtonType9());
                setStringParameter(stmt, 19, data.message().getButtonType10());
                setStringParameter(stmt, 20, data.message().getText());
                stmt.setString(21, data.errorMessage());
                stmt.setLong(22, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }

    private boolean isMessageExpired(WtsMessage message) {
        if (message.getExpires() == null) {
            return false;
        }
        log.debug("Checking if message is expired. expires = [{}]", message.getExpires());
        return message.getExpires().isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }

    private static boolean isMessageInvalid(WtsMessage message) {
        log.debug("Checking if message is invalid (messageCode or tplId is null), messageCode = [{}], tplId = [{}]",
                message.getMessageCode(), message.getTplId());
        return message.getMessageCode() == null || message.getTplId() == null;
    }
}
