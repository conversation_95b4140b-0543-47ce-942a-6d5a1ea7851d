package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.InboxMessage;
import com.homecredit.model.BatchData;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.IBM.enabled", havingValue = "true")
public class InboxSenderAgentService extends AbstractSenderAgentService<InboxMessage> {

    private final RabbitTemplate rabbitTemplate;

    public InboxSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    @Override
    public Agent getAgent() {
        return Agent.IBM;
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for INBOX agent. Total processed: {} records in {} batches",
                         totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for INBOX agent (total so far: {})",
                     batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            for (Map<String, Object> row : newRows) {

                Long id = (Long) getColumnValue(row, "ID");
                log.debug("Processing message with ID {} ...", id);
                InboxMessage message = prepareMessage(row);
                if (message.getUserId() == null && message.getCuid() == null) {
                    log.debug("Message with ID {} has null UserID and cuid. Marking as invalid", message.getId());
                    addToBatch(MessageStatus.FAILED, message, "NO GMA USER EXISTS to send prospect", FailedStatus.INVALID);
                } else if (isMessageExpired(message)) {
                    addToBatch(MessageStatus.FAILED, message, "Message is expired", FailedStatus.EXPIRED);
                } else if (message.getExternalMessageId() == null) {
                    log.debug("Message with ID {} has null contactId", id);
                    if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                        log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                        String errorMsg = "Message has null externalID for more than " + applicationConfig.getContactIdTimeout() + " minutes";
                        addToBatch(MessageStatus.FAILED, message, errorMsg, FailedStatus.EXPIRED);
                    } else {
                        log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                    }
                } else {
                    sendMessageToRabbit(message, id);
                }

                if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                    processBatch();
                }
            }

            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    private void sendMessageToRabbit(InboxMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(message);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalMessageId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalMessageId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalMessageId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        addToBatch(PROCESSED, message);
    }


    private InboxMessage prepareMessage(Map<String, Object> row) {
        InboxMessage message = new InboxMessage();
        if (getColumnValue(row, "cuid") != null) {
            message.setCuid(getLong((String) getColumnValue(row, "cuid")));
        }
        message.setText((String) getColumnValue(row, "text"));
        message.setTitle((String) getColumnValue(row, "title"));
        message.setImageUrl((String) getColumnValue(row, "imageUrl"));
        message.setCategoryName((String) getColumnValue(row, "ibm_categoryId"));
        message.setShouldNotify(longToBoolean((Long) getColumnValue(row, "should_notify")));
        message.setDateAvailableFrom(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setDateAvailableTo(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setGeneratedDateTime(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "generatedatedttm"))));
        message.setExternalMessageId((String) getColumnValue(row, "contact_id"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        message.setId((Long) getColumnValue(row, "ID"));
        message.setUserId((String) getColumnValue(row, "GMA_user_id"));
        return message;
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<InboxMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<InboxMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getExternalMessageId() != null ? data.message().getExternalMessageId() : "null");
                stmt.setString(2, data.failedStatus().toString());
                stmt.setString(3, data.errorMessage().substring(0, Math.min(data.errorMessage().length(), 36)));
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<InboxMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getExternalMessageId());
                stmt.setString(3, data.errorMessage());
                stmt.setLong(4, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }

    private boolean isMessageExpired(InboxMessage message) {
        if (message.getDateAvailableTo() == null) {
            log.debug("Checking if message is expired. dateAvailableTo = [null]");
            return false;
        }
        log.debug("Checking if message is expired. dateAvailableTo = [{}]", message.getDateAvailableTo());
        return message.getDateAvailableTo().isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }
}
