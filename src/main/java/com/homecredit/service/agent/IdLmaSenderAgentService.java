package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.DMMessageWrapper;
import com.homecredit.model.IdLmaAdditionalData;
import com.homecredit.model.IdLmaMessage;
import com.homecredit.model.IdLmaMessageAttribute;
import com.homecredit.model.BatchData;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.SimpleResourceHolder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.nio.ByteBuffer;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.IDLMA.enabled", havingValue = "true")
public class IdLmaSenderAgentService extends AbstractSenderAgentService<IdLmaMessage> {

    private final RabbitTemplate rabbitTemplate;

    public IdLmaSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    @Override
    public Agent getAgent() {
        return Agent.IDLMA;
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for IDLMA agent. Total processed: {} records in {} batches",
                         totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for IDLMA agent (total so far: {})",
                     batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            for (Map<String, Object> row : newRows) {

                Long id = (Long) getColumnValue(row, "ID");
                log.debug("Processing message with ID {} ...", id);
                IdLmaMessage message = prepareMessage(row);
                if (isMessageInvalid(message)) {
                    log.debug("Message with ID {} is invalid. Phone number is null", message.getId());
                    if (isTooLateForProcessingCuid(message)) {
                        log.debug("Message with ID {} is more than {} minutes old, marking as INVALID", message.getId(), applicationConfig.getCuidTimeout());
                        addToBatch(MessageStatus.FAILED, message, "Message is invalid - Phone number is null", FailedStatus.INVALID);
                    } else {
                        log.debug("Message with ID {} is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getCuidTimeout());
                    }
                } else if (message.getContactId() == null) {
                    log.debug("Message with ID {} has null contactId", id);
                    if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                        log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                        String errorMsg = "Message has null contactId for more than" + applicationConfig.getContactIdTimeout() + "minutes";
                        addToBatch(MessageStatus.FAILED, message, errorMsg, FailedStatus.EXPIRED);
                    } else {
                        log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                    }
                } else {
                    sendMessageToRabbit(message, id);
                }
    
                
                if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                    processBatch();
                }
            }

            // Process remaining batch
            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    private void sendMessageToRabbit(IdLmaMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        DMMessageWrapper messageWrapper = new DMMessageWrapper();
        messageWrapper.setMessages(Collections.singletonList(message));
        messageWrapper.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        messageWrapper.setPartnerId(applicationConfig.getPayload().get(getAgent()).getPartnerID());

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.ERROR, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        SimpleResourceHolder.bind(rabbitTemplate.getConnectionFactory(), "IDLMA");
        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", messageWrapper.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getMessageId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getMessageId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage(), FailedStatus.INVALID);
            return;
        } finally {
            SimpleResourceHolder.unbind(rabbitTemplate.getConnectionFactory());
        }
        addToBatch(MessageStatus.PROCESSED, message);
    }

    private IdLmaMessage prepareMessage(Map<String, Object> row) {
        IdLmaMessage message = new IdLmaMessage();
        String messageId = "SASCIPSH_" + convertToBase64((String) getColumnValue(row, "contact_id")).substring(0, 11);

        message.setPhoneNumber((String) getColumnValue(row, "PhoneNumber"));
        message.setWorkflow(applicationConfig.getPayload().get(getAgent()).getWorkflow());
        message.setMessageId(messageId);
        message.setContactId((String) getColumnValue(row, "contact_id"));

        IdLmaMessageAttribute attribute = new IdLmaMessageAttribute();
        attribute.setContractNum((String) getColumnValue(row, "PhoneNumber"));
        attribute.setCuid((String) getColumnValue(row, "cuid"));
        attribute.setTitle((String) getColumnValue(row, "Title"));
        attribute.setFullDetailMessage((String) getColumnValue(row, "FullDetailMessage"));
        attribute.setShortMessage((String) getColumnValue(row, "ShortMessage"));
        attribute.setImageUrl((String) getColumnValue(row, "ImageURL"));
        attribute.setLongUrl((String) getColumnValue(row, "DeepLink"));

        message.setMessageAttribute(attribute);
        try {
            message.setMessageAttributeString(objectMapper.writeValueAsString(attribute));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize message attribute for ID {}: {}", message.getId(), e.getMessage());
            message.setMessageAttributeString("{}"); // Set empty JSON as fallback
        }

        IdLmaAdditionalData additionalData = new IdLmaAdditionalData();
        additionalData.setCategoryId((String) getColumnValue(row, "CategoryID"));
        additionalData.setCategory((String) getColumnValue(row, "Category"));
        additionalData.setSubCategory((String) getColumnValue(row, "Subcategory"));
        additionalData.setRemark((String) getColumnValue(row, "Remark"));

        message.setAdditionalData(additionalData);
        try {
            message.setAdditionalDataString(objectMapper.writeValueAsString(additionalData));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize additional data for ID {}: {}", message.getId(), e.getMessage());
            message.setAdditionalDataString("{}"); // Set empty JSON as fallback
        }

        message.setId((Long) getColumnValue(row, "ID"));
        return message;
    }

    private String convertToBase64(String uuidString) {
        UUID uuid = UUID.fromString(uuidString);

        byte[] uuidBytes = ByteBuffer.wrap(new byte[16])
                .putLong(uuid.getMostSignificantBits())
                .putLong(uuid.getLeastSignificantBits())
                .array();

        return Base64.getEncoder().encodeToString(uuidBytes);
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<IdLmaMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<IdLmaMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getContactId() != null ? data.message().getContactId() : "null");
                stmt.setString(2, data.failedStatus().toString());
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<IdLmaMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getContactId());
                stmt.setString(3, data.message().getMessageId());
                stmt.setString(4, data.errorMessage());
                stmt.setLong(5, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }

    private static boolean isMessageInvalid(IdLmaMessage message) {
        log.debug("Checking if message is invalid (phoneNumber is null). phoneNumber = [{}]", message.getPhoneNumber());
        return message.getPhoneNumber() == null;
    }

    private boolean isTooLateForProcessingCuid(IdLmaMessage message) {
        ZonedDateTime zonedDateTime = message.getGeneratedDateTime();
        log.debug("Checking if message is too old old for processing cuid. generatedDateTime = [{}]", zonedDateTime.toString());
        return zonedDateTime.plusMinutes(applicationConfig.getCuidTimeout())
                .isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }
}
