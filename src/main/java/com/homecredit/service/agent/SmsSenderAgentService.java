package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.Attribute;
import com.homecredit.model.MessageWrapper;
import com.homecredit.model.SmsMessage;
import com.homecredit.model.BatchData;
import com.homecredit.service.AbstractSenderAgentService;
import com.homecredit.service.ExtApiGwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.SMS.enabled", havingValue = "true")
public class SmsSenderAgentService extends AbstractSenderAgentService<SmsMessage> {

    private final RabbitTemplate rabbitTemplate;
    private final ExtApiGwService extApiGwService;

    public SmsSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate,
            ExtApiGwService extApiGwService) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
        this.extApiGwService = extApiGwService;
    }

    @Override
    public Agent getAgent() {
        return Agent.SMS;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for SMS agent. Total processed: {} records in {} batches",
                         totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for SMS agent (total so far: {})",
                     batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            for (Map<String, Object> row : newRows) {

                Long id = (Long) getColumnValue(row, "ID");
                log.debug("Processing message with ID {} ...", id);
                SmsMessage message = prepareMessage(row);
                if (message.getTplId() == null) {
                    log.debug("Message with ID {} has null TPL ID. Obtaining TPL ID from REST API call.", id);
                    Long tplId = extApiGwService.getTplId(id, message.getCreativeId(), getAgent());
                    message.setTplId(tplId);
                } else if (isMessageExpired(message)) {
                    addToBatch(MessageStatus.FAILED, message, "Message is expired", FailedStatus.EXPIRED);
                } else if (isMessageInvalid(message)) {
                    addToBatch(MessageStatus.FAILED, message, "Message is invalid", FailedStatus.INVALID);
                } else if (message.getContactId() == null) {
                    log.debug("Message with ID {} has null contactId", id);
                    if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                        log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                        addToBatch(MessageStatus.FAILED, message, "Message has null externalID for more than 30 minutes", FailedStatus.EXPIRED);
                    } else {
                        log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                    }
                } else {
                    sendMessageToRabbit(message, id);
                }
    
                
                if (batchHolder.size() >= applicationConfig.getBatchSize()) {
                    processBatch();
                }
            }

            // Process remaining batch
            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    private void sendMessageToRabbit(SmsMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessage(Collections.singletonList(message));

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        addToBatch(PROCESSED, message);
    }


    private SmsMessage prepareMessage(Map<String, Object> row) {
        Long tplId = null;
        if (getColumnValue(row, "TPL_ID") != null) {
            tplId = (Long) getColumnValue(row, "TPL_ID");
        }

        SmsMessage message = new SmsMessage();
        message.setExternalId("SMS_" + getColumnValue(row, "contact_id"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        message.setMessageCode((String) getColumnValue(row, "MESSAGE_CODE"));
        message.setRecipient((String) getColumnValue(row, "PHONE_NUMBER"));
        message.setText((String) getColumnValue(row, "MSG_TEXT"));
        message.setEffectiveDate(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setExpires(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "ExpiresOnDttm"))));
        message.setValidFrom(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setValidTo(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setPriority((String) getColumnValue(row, "Priority"));
        message.setReportLevel(applicationConfig.getPayload().get(getAgent()).getReportLevel());
        List<Attribute> attributes = new ArrayList<>();
        attributes.add(new Attribute("SAS_MESSAGE_TYPE", applicationConfig.getPayload().get(getAgent()).getSasMessageType()));
        if (tplId != null) {
            attributes.add(new Attribute("SAS_TEMPLATE_ID", tplId.toString()));
        }
        String cuid = (String) getColumnValue(row, "cuid");
        if (cuid != null) {
            attributes.add(new Attribute("CUID", cuid));
        }
        message.setAttributes(attributes);
        message.setTplId(tplId);
        message.setSubject((String) getColumnValue(row, "Subject_id"));
        message.setId((Long) getColumnValue(row, "ID"));
        message.setCreativeId((String) getColumnValue(row, "creative_id"));
        return message;
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<SmsMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<SmsMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getContactId() != null ? data.message().getExternalId() : "null");
                stmt.setString(2, data.errorMessage().substring(0, Math.min(data.errorMessage().length(), 36)));
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<SmsMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getContactId() != null ? data.message().getExternalId() : null);
                if (data.message().getTplId() == null) {
                    stmt.setNull(3, Types.BIGINT);
                } else {
                    stmt.setLong(3, data.message().getTplId());
                }
                stmt.setString(4, data.message().getMessageCode());
                stmt.setString(5, data.errorMessage());
                stmt.setLong(6, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }

    private boolean isMessageExpired(SmsMessage message) {
        if (message.getExpires() == null || message.getValidFrom() == null || message.getValidTo() == null)
            return false;
        log.debug("Checking if message is expired. expires = [{}], validFrom = [{}], validTo = [{}]",
                message.getExpires(), message.getValidFrom(), message.getValidTo());
        return (message.getExpires().isBefore(message.getValidFrom()))
                || message.getExpires().isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }

    private static boolean isMessageInvalid(SmsMessage message) {
        log.debug("Checking if message is invalid (messageCode or tplId is null), messageCode = [{}], tplId = [{}]",
                message.getMessageCode(), message.getTplId());
        return message.getMessageCode() == null
                || message.getTplId() == null;
    }
}
